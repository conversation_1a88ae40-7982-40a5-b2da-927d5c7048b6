#!/usr/bin/env python3
"""
Simple WebSocket test server for testing WebSocket functionality
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import json
import asyncio
import time

app = FastAPI(title="WebSocket Test Server")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "WebSocket Test Server is running"}

@app.get("/health")
async def health():
    return {"status": "healthy", "timestamp": time.time()}

@app.websocket("/ws/agent")
async def websocket_agent_endpoint(websocket: WebSocket):
    """WebSocket endpoint for agent sessions - matches the main app structure"""
    # Get token from query params or headers
    token = websocket.query_params.get("token") or websocket.headers.get("sec-websocket-protocol")
    
    if not token:
        await websocket.close(code=4401)
        return
    
    # Simple token validation (just check if it exists)
    if token == "invalid":
        await websocket.close(code=4401)
        return
    
    # Accept connection with token as subprotocol
    await websocket.accept(subprotocol=token if token != "test-token" else None)
    
    try:
        # Send welcome message
        await websocket.send_text(json.dumps({
            "jsonrpc": "2.0",
            "event": {
                "type": "connection_established",
                "seq": 1,
                "timestamp": time.time()
            }
        }))
        
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                print(f"Received message: {message}")
                
                # Handle different message types
                if message.get("method") == "agent.run":
                    # Simulate agent run
                    session_id = f"session_{int(time.time())}"
                    
                    # Send start response
                    await websocket.send_text(json.dumps({
                        "jsonrpc": "2.0",
                        "id": message.get("id"),
                        "result": {
                            "status": "started",
                            "sessionId": session_id
                        }
                    }))
                    
                    # Simulate streaming tokens
                    prompt = message.get("params", {}).get("prompt", "Hello World")
                    words = prompt.split()
                    
                    for i, word in enumerate(words):
                        await asyncio.sleep(0.1)  # Simulate processing time
                        await websocket.send_text(json.dumps({
                            "jsonrpc": "2.0",
                            "event": {
                                "type": "token",
                                "seq": i + 2,
                                "text": word + " "
                            }
                        }))
                    
                    # Send completion
                    await websocket.send_text(json.dumps({
                        "jsonrpc": "2.0",
                        "event": {
                            "type": "status",
                            "seq": len(words) + 2,
                            "phase": "done"
                        }
                    }))
                
                elif message.get("method") == "ack":
                    # Handle acknowledgment
                    await websocket.send_text(json.dumps({
                        "jsonrpc": "2.0",
                        "id": message.get("id"),
                        "result": {"status": "acknowledged"}
                    }))
                
                else:
                    # Unknown method
                    await websocket.send_text(json.dumps({
                        "jsonrpc": "2.0",
                        "id": message.get("id"),
                        "error": {"code": -32601, "message": "Unknown method"}
                    }))
                    
            except json.JSONDecodeError:
                await websocket.send_text(json.dumps({
                    "jsonrpc": "2.0",
                    "error": {"code": -32600, "message": "Invalid JSON"}
                }))
                
    except WebSocketDisconnect:
        print("WebSocket disconnected")
    except Exception as e:
        print(f"WebSocket error: {e}")

@app.websocket("/ws/ai-agents/{connection_id}")
async def websocket_ai_agents_endpoint(websocket: WebSocket, connection_id: str):
    """WebSocket endpoint for AI agent updates - matches the v1 API structure"""
    await websocket.accept()
    
    try:
        # Send welcome message
        await websocket.send_text(json.dumps({
            "type": "connection_established",
            "connection_id": connection_id,
            "timestamp": time.time()
        }))
        
        while True:
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                print(f"AI Agents WebSocket received: {message}")
                
                # Echo the message back
                await websocket.send_text(json.dumps({
                    "type": "echo",
                    "original_message": message,
                    "timestamp": time.time()
                }))
                
            except json.JSONDecodeError:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Invalid JSON"
                }))
                
    except WebSocketDisconnect:
        print(f"AI Agents WebSocket disconnected: {connection_id}")
    except Exception as e:
        print(f"AI Agents WebSocket error: {e}")

if __name__ == "__main__":
    print("🚀 Starting WebSocket Test Server")
    print("📋 Available endpoints:")
    print("   • Health: http://localhost:8000/health")
    print("   • Agent WebSocket: ws://localhost:8000/ws/agent")
    print("   • AI Agents WebSocket: ws://localhost:8000/ws/ai-agents/{connection_id}")
    print("   • API Docs: http://localhost:8000/docs")
    print()
    print("🔑 Test with token: test-token")
    
    uvicorn.run(
        "websocket_test_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
