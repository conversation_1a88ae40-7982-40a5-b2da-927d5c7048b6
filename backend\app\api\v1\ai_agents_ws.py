"""
AI Agents WebSocket Endpoints.

This module provides WebSocket endpoints for real-time streaming communication
with AI agents, including live response streaming and status updates.
"""

import json
import asyncio
import uuid
from typing import Dict, Any, Set, Optional, AsyncGenerator
from datetime import datetime

from fastapi import WebSocket, WebSocketDisconnect, Depends, HTTPException, status
from fastapi.routing import APIRouter
import structlog

from ...core.auth import get_current_user_ws
from ...models.user import User
from ...services.agent_memory import get_memory_manager
from ...services.agent_orchestrator import get_agent_orchestrator, AgentRole, WorkflowTask
from ...services.model_router import get_model_router, ModelRequest

logger = structlog.get_logger(__name__)

# WebSocket router
ws_router = APIRouter()

# Connection manager for WebSocket connections
class ConnectionManager:
    """Manages WebSocket connections for real-time updates and streaming."""

    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_connections: Dict[str, Set[str]] = {}
        self.execution_subscribers: Dict[str, Set[str]] = {}
        self.workflow_subscribers: Dict[str, Set[str]] = {}
        self.streaming_sessions: Dict[str, Dict[str, Any]] = {}  # Track active streaming sessions

    async def connect(self, websocket: WebSocket, connection_id: str, user_id: str):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        self.active_connections[connection_id] = websocket

        if user_id not in self.user_connections:
            self.user_connections[user_id] = set()
        self.user_connections[user_id].add(connection_id)

        logger.info(f"WebSocket connected: {connection_id} for user {user_id}")

    def disconnect(self, connection_id: str, user_id: str):
        """Remove a WebSocket connection."""
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]

        if user_id in self.user_connections:
            self.user_connections[user_id].discard(connection_id)
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]

        # Remove from all subscriptions
        for execution_id in list(self.execution_subscribers.keys()):
            self.execution_subscribers[execution_id].discard(connection_id)
            if not self.execution_subscribers[execution_id]:
                del self.execution_subscribers[execution_id]

        for workflow_id in list(self.workflow_subscribers.keys()):
            self.workflow_subscribers[workflow_id].discard(connection_id)
            if not self.workflow_subscribers[workflow_id]:
                del self.workflow_subscribers[workflow_id]

        # Clean up streaming sessions
        if connection_id in self.streaming_sessions:
            del self.streaming_sessions[connection_id]

        logger.info(f"WebSocket disconnected: {connection_id}")

    async def send_personal_message(self, message: Dict[str, Any], connection_id: str):
        """Send a message to a specific connection."""
        if connection_id in self.active_connections:
            try:
                await self.active_connections[connection_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Failed to send message to {connection_id}: {e}")
                # Connection might be dead, remove it
                if connection_id in self.active_connections:
                    del self.active_connections[connection_id]

    async def send_to_user(self, message: Dict[str, Any], user_id: str):
        """Send a message to all connections for a user."""
        if user_id in self.user_connections:
            for connection_id in list(self.user_connections[user_id]):
                await self.send_personal_message(message, connection_id)

    def subscribe_to_execution(self, connection_id: str, execution_id: str):
        """Subscribe a connection to execution updates."""
        if execution_id not in self.execution_subscribers:
            self.execution_subscribers[execution_id] = set()
        self.execution_subscribers[execution_id].add(connection_id)

    def subscribe_to_workflow(self, connection_id: str, workflow_id: str):
        """Subscribe a connection to workflow updates."""
        if workflow_id not in self.workflow_subscribers:
            self.workflow_subscribers[workflow_id] = set()
        self.workflow_subscribers[workflow_id].add(connection_id)

    async def stream_to_connection(self, connection_id: str, stream_type: str, data: Any):
        """Stream data to a specific connection."""
        if connection_id in self.active_connections:
            message = {
                "type": "stream",
                "stream_type": stream_type,
                "data": data,
                "timestamp": datetime.utcnow().isoformat()
            }
            await self.send_personal_message(message, connection_id)

    async def start_streaming_session(self, connection_id: str, session_id: str, session_type: str):
        """Start a new streaming session."""
        self.streaming_sessions[connection_id] = {
            "session_id": session_id,
            "session_type": session_type,
            "started_at": datetime.utcnow(),
            "active": True
        }

        await self.send_personal_message({
            "type": "stream_start",
            "session_id": session_id,
            "session_type": session_type,
            "timestamp": datetime.utcnow().isoformat()
        }, connection_id)

    async def end_streaming_session(self, connection_id: str, session_id: str):
        """End a streaming session."""
        if connection_id in self.streaming_sessions:
            self.streaming_sessions[connection_id]["active"] = False

            await self.send_personal_message({
                "type": "stream_end",
                "session_id": session_id,
                "timestamp": datetime.utcnow().isoformat()
            }, connection_id)

    async def broadcast_execution_update(self, execution_id: str, update: Dict[str, Any]):
        """Broadcast execution update to all subscribers."""
        if execution_id in self.execution_subscribers:
            message = {
                "type": "execution_update",
                "execution_id": execution_id,
                "data": update,
                "timestamp": datetime.utcnow().isoformat()
            }

            for connection_id in list(self.execution_subscribers[execution_id]):
                await self.send_personal_message(message, connection_id)

    async def broadcast_workflow_update(self, workflow_id: str, update: Dict[str, Any]):
        """Broadcast workflow update to all subscribers."""
        if workflow_id in self.workflow_subscribers:
            message = {
                "type": "workflow_update",
                "workflow_id": workflow_id,
                "data": update,
                "timestamp": datetime.utcnow().isoformat()
            }

            for connection_id in list(self.workflow_subscribers[workflow_id]):
                await self.send_personal_message(message, connection_id)

    def get_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""
        return {
            "total_connections": len(self.active_connections),
            "users_connected": len(self.user_connections),
            "execution_subscriptions": len(self.execution_subscribers),
            "workflow_subscriptions": len(self.workflow_subscribers)
        }


# Global connection manager
manager = ConnectionManager()


@ws_router.websocket("/ws/ai-agents/{connection_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    connection_id: str,
    token: str = None
):
    """WebSocket endpoint for real-time AI agent updates."""
    try:
        # Authenticate user (simplified - in production, use proper token validation)
        if not token:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return

        # For now, extract user_id from token (in production, validate JWT)
        user_id = token  # Simplified - replace with actual JWT validation

        await manager.connect(websocket, connection_id, user_id)

        # Send welcome message
        await manager.send_personal_message({
            "type": "connection_established",
            "connection_id": connection_id,
            "timestamp": datetime.utcnow().isoformat()
        }, connection_id)

        try:
            while True:
                # Receive messages from client
                data = await websocket.receive_text()
                message = json.loads(data)

                await handle_websocket_message(connection_id, user_id, message)

        except WebSocketDisconnect:
            manager.disconnect(connection_id, user_id)
            logger.info(f"WebSocket disconnected: {connection_id}")

    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        try:
            await websocket.close(code=status.WS_1011_INTERNAL_ERROR)
        except:
            pass


async def handle_websocket_message(connection_id: str, user_id: str, message: Dict[str, Any]):
    """Handle incoming WebSocket messages."""
    try:
        message_type = message.get("type")

        if message_type == "subscribe_execution":
            execution_id = message.get("execution_id")
            if execution_id:
                manager.subscribe_to_execution(connection_id, execution_id)
                await manager.send_personal_message({
                    "type": "subscription_confirmed",
                    "subscription_type": "execution",
                    "execution_id": execution_id
                }, connection_id)

        elif message_type == "subscribe_workflow":
            workflow_id = message.get("workflow_id")
            if workflow_id:
                manager.subscribe_to_workflow(connection_id, workflow_id)
                await manager.send_personal_message({
                    "type": "subscription_confirmed",
                    "subscription_type": "workflow",
                    "workflow_id": workflow_id
                }, connection_id)

        elif message_type == "ping":
            await manager.send_personal_message({
                "type": "pong",
                "timestamp": datetime.utcnow().isoformat()
            }, connection_id)

        elif message_type == "get_stats":
            stats = manager.get_stats()
            await manager.send_personal_message({
                "type": "stats",
                "data": stats
            }, connection_id)

        elif message_type == "stream_agent_request":
            # Handle streaming agent execution request
            await handle_streaming_agent_request(connection_id, user_id, message)

        elif message_type == "stream_chat_message":
            # Handle streaming chat message to help agent
            await handle_streaming_chat_message(connection_id, user_id, message)

        else:
            await manager.send_personal_message({
                "type": "error",
                "message": f"Unknown message type: {message_type}"
            }, connection_id)

    except Exception as e:
        logger.error(f"Error handling WebSocket message: {e}")
        await manager.send_personal_message({
            "type": "error",
            "message": "Failed to process message"
        }, connection_id)


# Progress tracking functions that can be called from agent execution
async def notify_execution_progress(execution_id: str, progress: float, status: str, details: Dict[str, Any] = None):
    """Notify WebSocket subscribers about execution progress."""
    update = {
        "progress": progress,
        "status": status,
        "details": details or {},
        "updated_at": datetime.utcnow().isoformat()
    }

    await manager.broadcast_execution_update(execution_id, update)


async def notify_workflow_progress(workflow_id: str, progress: float, status: str, details: Dict[str, Any] = None):
    """Notify WebSocket subscribers about workflow progress."""
    update = {
        "progress": progress,
        "status": status,
        "details": details or {},
        "updated_at": datetime.utcnow().isoformat()
    }

    await manager.broadcast_workflow_update(workflow_id, update)


async def notify_agent_event(user_id: str, event_type: str, data: Dict[str, Any]):
    """Notify a specific user about an agent event."""
    message = {
        "type": "agent_event",
        "event_type": event_type,
        "data": data,
        "timestamp": datetime.utcnow().isoformat()
    }

    await manager.send_to_user(message, user_id)


# Background task for periodic updates
async def start_progress_monitor():
    """Start background task for monitoring and broadcasting progress updates."""
    while True:
        try:
            # Check for any pending updates from the memory manager
            memory_manager = get_memory_manager()

            # Get collaboration summary
            summary = await memory_manager.get_collaboration_summary()

            # Broadcast system status to all connected users
            if manager.active_connections:
                system_status = {
                    "type": "system_status",
                    "data": {
                        "active_workflows": summary.get("workflow_states", 0),
                        "active_agents": summary.get("active_agents", 0),
                        "performance_metrics": summary.get("performance_metrics", 0),
                        "timestamp": datetime.utcnow().isoformat()
                    }
                }

                # Send to all connections (could be optimized to send only to interested users)
                for connection_id in list(manager.active_connections.keys()):
                    await manager.send_personal_message(system_status, connection_id)

            # Wait before next update
            await asyncio.sleep(30)  # Update every 30 seconds

        except Exception as e:
            logger.error(f"Progress monitor error: {e}")
            await asyncio.sleep(60)  # Wait longer on error


# Function to get connection manager (for use in other modules)
def get_connection_manager() -> ConnectionManager:
    """Get the global connection manager instance."""
    return manager


# Streaming agent request handlers
async def handle_streaming_agent_request(connection_id: str, user_id: str, message: Dict[str, Any]):
    """Handle streaming agent execution request."""
    try:
        agent_role = message.get("agent_role")
        task_description = message.get("task_description", "")
        input_data = message.get("input_data", {})
        session_id = message.get("session_id", str(uuid.uuid4()))

        # Validate agent role
        if agent_role not in [role.value for role in AgentRole]:
            await manager.send_personal_message({
                "type": "error",
                "message": f"Invalid agent role: {agent_role}"
            }, connection_id)
            return

        # Start streaming session
        await manager.start_streaming_session(connection_id, session_id, "agent_execution")

        # Execute agent with streaming
        await execute_agent_with_streaming(
            connection_id, user_id, session_id, agent_role, task_description, input_data
        )

    except Exception as e:
        logger.error(f"Error handling streaming agent request: {e}")
        await manager.send_personal_message({
            "type": "error",
            "message": "Failed to process streaming agent request"
        }, connection_id)


async def handle_streaming_chat_message(connection_id: str, user_id: str, message: Dict[str, Any]):
    """Handle streaming chat message to help agent."""
    try:
        chat_message = message.get("message", "")
        context = message.get("context", {})
        session_id = message.get("session_id", str(uuid.uuid4()))

        if not chat_message.strip():
            await manager.send_personal_message({
                "type": "error",
                "message": "Empty chat message"
            }, connection_id)
            return

        # Start streaming session
        await manager.start_streaming_session(connection_id, session_id, "chat_response")

        # Execute help agent with streaming
        await execute_help_agent_with_streaming(
            connection_id, user_id, session_id, chat_message, context
        )

    except Exception as e:
        logger.error(f"Error handling streaming chat message: {e}")
        await manager.send_personal_message({
            "type": "error",
            "message": "Failed to process streaming chat message"
        }, connection_id)


# Health check endpoint for WebSocket service
@ws_router.get("/ws/health")
async def websocket_health():
    """Health check for WebSocket service."""
    stats = manager.get_stats()
    return {
        "status": "healthy",
        "websocket_stats": stats,
        "timestamp": datetime.utcnow().isoformat()
    }


async def execute_agent_with_streaming(
    connection_id: str,
    user_id: str,
    session_id: str,
    agent_role: str,
    task_description: str,
    input_data: Dict[str, Any]
):
    """Execute an agent with streaming output."""
    try:
        # Get orchestrator and model router
        orchestrator = get_agent_orchestrator()
        model_router = get_model_router()

        # Stream initial status
        await manager.stream_to_connection(connection_id, "status", {
            "status": "initializing",
            "message": f"Initializing {agent_role} agent...",
            "progress": 10
        })

        # Create a simple workflow for the agent
        workflow_tasks = [WorkflowTask(
            id=str(uuid.uuid4()),
            description=task_description,
            expected_output="Processed result from the agent",
            agent_role=agent_role_enum,
            input_data=input_data,
            dependencies=[]
        )]

        # Create workflow
        workflow_id = await orchestrator.create_workflow(
            tasks=workflow_tasks,
            user_id=user_id
        )

        # Define progress callback for streaming updates
        async def progress_callback(update):
            await manager.stream_to_connection(connection_id, "workflow_progress", {
                "workflow_id": workflow_id,
                "agent_role": agent_role,
                **update
            })

        # Execute workflow with streaming
        try:
            result = await orchestrator.execute_workflow_with_streaming(
                workflow_id, progress_callback
            )

            # Stream final result
            await manager.stream_to_connection(connection_id, "response_chunk", {
                "content": result.results.get("output", "Task completed successfully"),
                "agent_role": agent_role,
                "final": True
            })
        except Exception as workflow_error:
            logger.error(f"Workflow execution error: {workflow_error}")
            await manager.stream_to_connection(connection_id, "error", {
                "message": f"Workflow execution failed: {str(workflow_error)}",
                "agent_role": agent_role
            })

        # Stream completion
        await manager.stream_to_connection(connection_id, "status", {
            "status": "completed",
            "message": f"Agent {agent_role} has completed the task",
            "progress": 100
        })

        # End streaming session
        await manager.end_streaming_session(connection_id, session_id)

    except Exception as e:
        logger.error(f"Error in streaming agent execution: {e}")
        await manager.stream_to_connection(connection_id, "error", {
            "message": str(e),
            "agent_role": agent_role
        })
        await manager.end_streaming_session(connection_id, session_id)


async def execute_help_agent_with_streaming(
    connection_id: str,
    user_id: str,
    session_id: str,
    chat_message: str,
    context: Dict[str, Any]
):
    """Execute help agent with streaming chat response."""
    try:
        # Get model router
        model_router = get_model_router()

        # Stream initial status
        await manager.stream_to_connection(connection_id, "status", {
            "status": "thinking",
            "message": "AI assistant is thinking...",
            "progress": 20
        })

        # Create model request for chat
        model_request = ModelRequest(
            messages=[{
                "role": "user",
                "content": f"User message: {chat_message}\nContext: {json.dumps(context)}"
            }],
            max_tokens=1500,
            temperature=0.8,
            stream=True
        )

        # Stream the chat response
        async for chunk in model_router.generate_stream(model_request):
            await manager.stream_to_connection(connection_id, "chat_chunk", {
                "content": chunk,
                "type": "assistant"
            })

        # Stream completion
        await manager.stream_to_connection(connection_id, "status", {
            "status": "completed",
            "message": "Response complete",
            "progress": 100
        })

        # End streaming session
        await manager.end_streaming_session(connection_id, session_id)

    except Exception as e:
        logger.error(f"Error in streaming chat execution: {e}")
        await manager.stream_to_connection(connection_id, "error", {
            "message": str(e)
        })
        await manager.end_streaming_session(connection_id, session_id)
