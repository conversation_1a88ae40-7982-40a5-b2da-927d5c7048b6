<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Streaming Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.connecting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .chat-container {
            border: 1px solid #ddd;
            height: 400px;
            overflow-y: auto;
            padding: 10px;
            margin: 10px 0;
            background-color: #fafafa;
        }
        .message {
            margin: 5px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .message.user {
            background-color: #007bff;
            color: white;
            text-align: right;
        }
        .message.agent {
            background-color: #e9ecef;
            color: #333;
        }
        .message.system {
            background-color: #fff3cd;
            color: #856404;
            font-style: italic;
        }
        .message.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .streaming {
            background-color: #d1ecf1;
            color: #0c5460;
            border-left: 4px solid #bee5eb;
        }
        .input-group {
            display: flex;
            gap: 10px;
            margin: 10px 0;
        }
        .input-group input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .input-group button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .input-group button:hover {
            background-color: #0056b3;
        }
        .input-group button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            flex-wrap: wrap;
        }
        .test-buttons button {
            padding: 8px 16px;
            border: 1px solid #007bff;
            background-color: white;
            color: #007bff;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-buttons button:hover {
            background-color: #007bff;
            color: white;
        }
        .stats {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 WebSocket Streaming Test</h1>
        
        <div id="status" class="status disconnected">
            Disconnected
        </div>
        
        <div class="input-group">
            <input type="text" id="serverUrl" value="ws://localhost:8000/api/v1/ws/ai-agents/test123?token=test-user" placeholder="WebSocket URL">
            <button id="connectBtn" onclick="connect()">Connect</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
        </div>
        
        <div class="test-buttons">
            <button onclick="testPing()">Test Ping</button>
            <button onclick="testStats()">Get Stats</button>
            <button onclick="testStreamingChat()">Test Streaming Chat</button>
            <button onclick="testStreamingAgent()">Test Streaming Agent</button>
            <button onclick="clearMessages()">Clear Messages</button>
        </div>
        
        <div class="chat-container" id="messages"></div>
        
        <div class="input-group">
            <input type="text" id="messageInput" placeholder="Type a message..." onkeypress="handleKeyPress(event)">
            <button onclick="sendMessage()">Send Message</button>
        </div>
        
        <div class="stats" id="stats">
            Connection stats will appear here...
        </div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        let streamingMessage = '';
        let isStreaming = false;

        function updateStatus(status, className) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = status;
            statusEl.className = `status ${className}`;
        }

        function addMessage(content, type = 'system') {
            const messagesEl = document.getElementById('messages');
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            messageEl.innerHTML = content;
            messagesEl.appendChild(messageEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }

        function updateStreamingMessage(content) {
            const messagesEl = document.getElementById('messages');
            let streamingEl = document.querySelector('.message.streaming');
            
            if (!streamingEl) {
                streamingEl = document.createElement('div');
                streamingEl.className = 'message streaming';
                messagesEl.appendChild(streamingEl);
            }
            
            streamingEl.innerHTML = content + '<span style="animation: blink 1s infinite;">|</span>';
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }

        function finalizeStreamingMessage(content) {
            const streamingEl = document.querySelector('.message.streaming');
            if (streamingEl) {
                streamingEl.className = 'message agent';
                streamingEl.innerHTML = content;
            }
        }

        function connect() {
            const url = document.getElementById('serverUrl').value;
            updateStatus('Connecting...', 'connecting');
            
            try {
                ws = new WebSocket(url);
                
                ws.onopen = function() {
                    isConnected = true;
                    updateStatus('Connected', 'connected');
                    document.getElementById('connectBtn').disabled = true;
                    document.getElementById('disconnectBtn').disabled = false;
                    addMessage('🔌 Connected to WebSocket server', 'system');
                };
                
                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        handleMessage(data);
                    } catch (e) {
                        addMessage(`📨 Raw message: ${event.data}`, 'system');
                    }
                };
                
                ws.onclose = function() {
                    isConnected = false;
                    updateStatus('Disconnected', 'disconnected');
                    document.getElementById('connectBtn').disabled = false;
                    document.getElementById('disconnectBtn').disabled = true;
                    addMessage('🔌 Disconnected from WebSocket server', 'system');
                };
                
                ws.onerror = function(error) {
                    addMessage(`❌ WebSocket error: ${error}`, 'error');
                };
                
            } catch (error) {
                updateStatus('Connection failed', 'disconnected');
                addMessage(`❌ Failed to connect: ${error}`, 'error');
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        function handleMessage(data) {
            console.log('Received message:', data);
            
            switch (data.type) {
                case 'connection_established':
                    addMessage(`✅ Connection established: ${data.connection_id}`, 'system');
                    break;
                    
                case 'pong':
                    addMessage('🏓 Pong received', 'system');
                    break;
                    
                case 'stats':
                    document.getElementById('stats').textContent = JSON.stringify(data.data, null, 2);
                    addMessage('📊 Stats updated', 'system');
                    break;
                    
                case 'stream_start':
                    isStreaming = true;
                    streamingMessage = '';
                    addMessage(`🚀 Stream started: ${data.session_type || 'unknown'}`, 'system');
                    break;
                    
                case 'stream':
                    if (data.stream_type === 'chat_chunk') {
                        streamingMessage += data.data.content;
                        updateStreamingMessage(streamingMessage);
                    } else if (data.stream_type === 'status') {
                        addMessage(`📊 Status: ${data.data.message} (${data.data.progress}%)`, 'system');
                    } else if (data.stream_type === 'response_chunk') {
                        streamingMessage += data.data.content;
                        updateStreamingMessage(streamingMessage);
                    }
                    break;
                    
                case 'stream_end':
                    isStreaming = false;
                    if (streamingMessage) {
                        finalizeStreamingMessage(streamingMessage);
                    }
                    addMessage('🏁 Stream ended', 'system');
                    streamingMessage = '';
                    break;
                    
                case 'error':
                    addMessage(`❌ Error: ${data.message}`, 'error');
                    break;
                    
                default:
                    addMessage(`📨 ${data.type}: ${JSON.stringify(data)}`, 'system');
            }
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || !isConnected) return;
            
            addMessage(message, 'user');
            
            const chatMessage = {
                type: 'stream_chat_message',
                message: message,
                context: { page: 'test' },
                session_id: `test_${Date.now()}`
            };
            
            ws.send(JSON.stringify(chatMessage));
            input.value = '';
        }

        function testPing() {
            if (!isConnected) return;
            
            const pingMessage = {
                type: 'ping',
                timestamp: new Date().toISOString()
            };
            
            ws.send(JSON.stringify(pingMessage));
            addMessage('🏓 Ping sent', 'user');
        }

        function testStats() {
            if (!isConnected) return;
            
            const statsMessage = {
                type: 'get_stats'
            };
            
            ws.send(JSON.stringify(statsMessage));
            addMessage('📊 Stats requested', 'user');
        }

        function testStreamingChat() {
            if (!isConnected) return;
            
            const chatMessage = {
                type: 'stream_chat_message',
                message: 'Hello! Can you help me with contract management?',
                context: { page: 'test' },
                session_id: `test_chat_${Date.now()}`
            };
            
            ws.send(JSON.stringify(chatMessage));
            addMessage('💬 Streaming chat test started', 'user');
        }

        function testStreamingAgent() {
            if (!isConnected) return;
            
            const agentMessage = {
                type: 'stream_agent_request',
                agent_role: 'help_agent',
                task_description: 'Provide help with document management',
                input_data: { context: 'test' },
                session_id: `test_agent_${Date.now()}`
            };
            
            ws.send(JSON.stringify(agentMessage));
            addMessage('🤖 Streaming agent test started', 'user');
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // Add CSS animation for blinking cursor
        const style = document.createElement('style');
        style.textContent = `
            @keyframes blink {
                0%, 50% { opacity: 1; }
                51%, 100% { opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
