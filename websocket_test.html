<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ccc;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
        }
        input, textarea {
            width: 100%;
            padding: 5px;
            margin: 5px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
        .connecting { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>WebSocket Test Page</h1>
    
    <div class="container">
        <h2>Connection Settings</h2>
        <label>WebSocket URL:</label>
        <input type="text" id="wsUrl" value="ws://localhost:8000/ws/agent">
        
        <label>Token:</label>
        <input type="text" id="token" value="test-token">
        
        <div>
            <button onclick="connect()">Connect</button>
            <button onclick="disconnect()">Disconnect</button>
        </div>
        
        <div id="status" class="status disconnected">Disconnected</div>
    </div>
    
    <div class="container">
        <h2>Send Message</h2>
        <label>Message Type:</label>
        <select id="messageType">
            <option value="agent.run">agent.run</option>
            <option value="ack">ack</option>
            <option value="agent.cancel">agent.cancel</option>
            <option value="session.resume">session.resume</option>
        </select>
        
        <label>Prompt (for agent.run):</label>
        <input type="text" id="prompt" value="Hello, how are you today?">
        
        <label>Custom JSON Message:</label>
        <textarea id="customMessage" rows="4" placeholder='{"jsonrpc": "2.0", "id": "1", "method": "agent.run", "params": {"prompt": "test"}}'></textarea>
        
        <div>
            <button onclick="sendAgentRun()">Send Agent Run</button>
            <button onclick="sendAck()">Send Ack</button>
            <button onclick="sendCustom()">Send Custom</button>
        </div>
    </div>
    
    <div class="container">
        <h2>Message Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        let ws = null;
        let messageId = 1;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'sent' ? 'blue' : type === 'received' ? 'green' : 'black';
            logDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${type.toUpperCase()}: ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(status, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = status;
            statusDiv.className = `status ${className}`;
        }
        
        function connect() {
            const url = document.getElementById('wsUrl').value;
            const token = document.getElementById('token').value;
            
            if (ws) {
                ws.close();
            }
            
            try {
                updateStatus('Connecting...', 'connecting');
                log(`Connecting to ${url} with token: ${token}`);
                
                // Try connecting with token as subprotocol
                ws = new WebSocket(url, token);
                
                ws.onopen = function(event) {
                    updateStatus('Connected', 'connected');
                    log('WebSocket connected successfully');
                };
                
                ws.onmessage = function(event) {
                    log(`Received: ${event.data}`, 'received');
                    
                    try {
                        const message = JSON.parse(event.data);
                        if (message.event && message.event.type === 'token') {
                            // Handle streaming tokens
                            log(`Token: "${message.event.text}"`, 'received');
                        }
                    } catch (e) {
                        // Not JSON, that's okay
                    }
                };
                
                ws.onclose = function(event) {
                    updateStatus('Disconnected', 'disconnected');
                    log(`WebSocket closed. Code: ${event.code}, Reason: ${event.reason}`);
                };
                
                ws.onerror = function(error) {
                    updateStatus('Error', 'disconnected');
                    log(`WebSocket error: ${error}`, 'error');
                };
                
            } catch (error) {
                updateStatus('Error', 'disconnected');
                log(`Connection error: ${error}`, 'error');
            }
        }
        
        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }
        
        function sendMessage(message) {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('WebSocket is not connected', 'error');
                return;
            }
            
            const messageStr = JSON.stringify(message);
            ws.send(messageStr);
            log(`Sent: ${messageStr}`, 'sent');
        }
        
        function sendAgentRun() {
            const prompt = document.getElementById('prompt').value;
            const message = {
                jsonrpc: "2.0",
                id: String(messageId++),
                method: "agent.run",
                params: {
                    prompt: prompt
                }
            };
            sendMessage(message);
        }
        
        function sendAck() {
            const message = {
                jsonrpc: "2.0",
                id: String(messageId++),
                method: "ack",
                params: {
                    lastSeq: 10
                }
            };
            sendMessage(message);
        }
        
        function sendCustom() {
            const customText = document.getElementById('customMessage').value;
            if (!customText.trim()) {
                log('Please enter a custom message', 'error');
                return;
            }
            
            try {
                const message = JSON.parse(customText);
                sendMessage(message);
            } catch (error) {
                log(`Invalid JSON: ${error}`, 'error');
            }
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Auto-connect on page load
        window.onload = function() {
            log('WebSocket test page loaded');
        };
    </script>
</body>
</html>
