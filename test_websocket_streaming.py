#!/usr/bin/env python3
"""
Test script for WebSocket streaming implementation.

This script tests the WebSocket streaming functionality for AI agents
including connection, message handling, and streaming responses.
"""

import asyncio
import json
import websockets
import time
from typing import Dict, Any

class WebSocketStreamingTester:
    """Test class for WebSocket streaming functionality."""
    
    def __init__(self, base_url: str = "ws://localhost:8000"):
        self.base_url = base_url
        self.connection_id = f"test_{int(time.time())}"
        self.websocket = None
        
    async def connect(self):
        """Connect to the WebSocket endpoint."""
        try:
            url = f"{self.base_url}/api/v1/ws/ai-agents/{self.connection_id}?token=test-user"
            print(f"Connecting to: {url}")
            
            self.websocket = await websockets.connect(url)
            print("✅ WebSocket connected successfully")
            
            # Wait for welcome message
            welcome_msg = await self.websocket.recv()
            welcome_data = json.loads(welcome_msg)
            print(f"📨 Welcome message: {welcome_data}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to connect: {e}")
            return False
    
    async def test_ping_pong(self):
        """Test basic ping-pong functionality."""
        print("\n🏓 Testing ping-pong...")
        
        ping_message = {
            "type": "ping",
            "timestamp": time.time()
        }
        
        await self.websocket.send(json.dumps(ping_message))
        response = await self.websocket.recv()
        response_data = json.loads(response)
        
        if response_data.get("type") == "pong":
            print("✅ Ping-pong test passed")
            return True
        else:
            print(f"❌ Ping-pong test failed: {response_data}")
            return False
    
    async def test_streaming_chat(self):
        """Test streaming chat functionality."""
        print("\n💬 Testing streaming chat...")
        
        chat_message = {
            "type": "stream_chat_message",
            "message": "Hello, can you help me with contract management?",
            "context": {"page": "dashboard"},
            "session_id": f"chat_test_{int(time.time())}"
        }
        
        await self.websocket.send(json.dumps(chat_message))
        print("📤 Sent chat message")
        
        # Collect streaming responses
        responses = []
        stream_started = False
        stream_ended = False
        
        try:
            while not stream_ended:
                response = await asyncio.wait_for(self.websocket.recv(), timeout=10.0)
                response_data = json.loads(response)
                responses.append(response_data)
                
                print(f"📨 Received: {response_data.get('type', 'unknown')}")
                
                if response_data.get("type") == "stream_start":
                    stream_started = True
                    print("🚀 Stream started")
                elif response_data.get("type") == "stream":
                    if response_data.get("stream_type") == "chat_chunk":
                        content = response_data.get("data", {}).get("content", "")
                        print(f"💭 Chunk: {content}")
                elif response_data.get("type") == "stream_end":
                    stream_ended = True
                    print("🏁 Stream ended")
                elif response_data.get("type") == "error":
                    print(f"❌ Error: {response_data.get('message')}")
                    break
                    
        except asyncio.TimeoutError:
            print("⏰ Timeout waiting for streaming response")
            return False
        
        if stream_started and stream_ended:
            print("✅ Streaming chat test passed")
            return True
        else:
            print("❌ Streaming chat test failed")
            return False
    
    async def test_streaming_agent_request(self):
        """Test streaming agent execution."""
        print("\n🤖 Testing streaming agent request...")
        
        agent_request = {
            "type": "stream_agent_request",
            "agent_role": "help_agent",
            "task_description": "Provide help with document management",
            "input_data": {"context": "dashboard"},
            "session_id": f"agent_test_{int(time.time())}"
        }
        
        await self.websocket.send(json.dumps(agent_request))
        print("📤 Sent agent request")
        
        # Collect streaming responses
        responses = []
        stream_started = False
        stream_ended = False
        
        try:
            while not stream_ended:
                response = await asyncio.wait_for(self.websocket.recv(), timeout=15.0)
                response_data = json.loads(response)
                responses.append(response_data)
                
                print(f"📨 Received: {response_data.get('type', 'unknown')}")
                
                if response_data.get("type") == "stream_start":
                    stream_started = True
                    print("🚀 Agent stream started")
                elif response_data.get("type") == "stream":
                    stream_type = response_data.get("stream_type")
                    if stream_type == "status":
                        status = response_data.get("data", {})
                        print(f"📊 Status: {status.get('message', 'Unknown')}")
                    elif stream_type == "response_chunk":
                        content = response_data.get("data", {}).get("content", "")
                        print(f"🧠 Agent response: {content}")
                elif response_data.get("type") == "stream_end":
                    stream_ended = True
                    print("🏁 Agent stream ended")
                elif response_data.get("type") == "error":
                    print(f"❌ Error: {response_data.get('message')}")
                    break
                    
        except asyncio.TimeoutError:
            print("⏰ Timeout waiting for agent response")
            return False
        
        if stream_started:
            print("✅ Streaming agent test passed")
            return True
        else:
            print("❌ Streaming agent test failed")
            return False
    
    async def test_stats(self):
        """Test stats retrieval."""
        print("\n📊 Testing stats...")
        
        stats_message = {
            "type": "get_stats"
        }
        
        await self.websocket.send(json.dumps(stats_message))
        response = await self.websocket.recv()
        response_data = json.loads(response)
        
        if response_data.get("type") == "stats":
            stats = response_data.get("data", {})
            print(f"📈 Stats: {stats}")
            print("✅ Stats test passed")
            return True
        else:
            print(f"❌ Stats test failed: {response_data}")
            return False
    
    async def disconnect(self):
        """Disconnect from WebSocket."""
        if self.websocket:
            await self.websocket.close()
            print("🔌 WebSocket disconnected")

async def main():
    """Main test function."""
    print("🧪 WebSocket Streaming Test Suite")
    print("=" * 50)
    
    tester = WebSocketStreamingTester()
    
    # Connect
    if not await tester.connect():
        return
    
    # Run tests
    tests = [
        ("Ping-Pong", tester.test_ping_pong),
        ("Stats", tester.test_stats),
        ("Streaming Chat", tester.test_streaming_chat),
        ("Streaming Agent", tester.test_streaming_agent_request),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = await test_func()
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results[test_name] = False
    
    # Disconnect
    await tester.disconnect()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Results Summary:")
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! WebSocket streaming is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the implementation.")

if __name__ == "__main__":
    asyncio.run(main())
