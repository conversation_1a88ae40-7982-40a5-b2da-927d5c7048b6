#!/usr/bin/env python3
"""
Very simple WebSocket server for testing
"""

import asyncio
import websockets
import json
import time

async def handle_websocket(websocket, path):
    print(f"New WebSocket connection: {path}")
    
    try:
        # Send welcome message
        welcome = {
            "jsonrpc": "2.0",
            "event": {
                "type": "connection_established",
                "seq": 1,
                "timestamp": time.time()
            }
        }
        await websocket.send(json.dumps(welcome))
        
        async for message in websocket:
            print(f"Received: {message}")
            
            try:
                data = json.loads(message)
                
                if data.get("method") == "agent.run":
                    # Send start response
                    response = {
                        "jsonrpc": "2.0",
                        "id": data.get("id"),
                        "result": {
                            "status": "started",
                            "sessionId": f"session_{int(time.time())}"
                        }
                    }
                    await websocket.send(json.dumps(response))
                    
                    # Simulate streaming
                    prompt = data.get("params", {}).get("prompt", "Hello")
                    words = prompt.split()
                    
                    for i, word in enumerate(words):
                        await asyncio.sleep(0.1)
                        token_event = {
                            "jsonrpc": "2.0",
                            "event": {
                                "type": "token",
                                "seq": i + 2,
                                "text": word + " "
                            }
                        }
                        await websocket.send(json.dumps(token_event))
                    
                    # Send completion
                    completion = {
                        "jsonrpc": "2.0",
                        "event": {
                            "type": "status",
                            "seq": len(words) + 2,
                            "phase": "done"
                        }
                    }
                    await websocket.send(json.dumps(completion))
                
                else:
                    # Echo other messages
                    response = {
                        "jsonrpc": "2.0",
                        "id": data.get("id"),
                        "result": {"status": "received", "echo": data}
                    }
                    await websocket.send(json.dumps(response))
                    
            except json.JSONDecodeError:
                error = {
                    "jsonrpc": "2.0",
                    "error": {"code": -32600, "message": "Invalid JSON"}
                }
                await websocket.send(json.dumps(error))
                
    except websockets.exceptions.ConnectionClosed:
        print("WebSocket connection closed")
    except Exception as e:
        print(f"WebSocket error: {e}")

async def main():
    print("Starting simple WebSocket server on ws://localhost:8000/ws/agent")
    
    # Start server
    server = await websockets.serve(
        handle_websocket,
        "localhost",
        8000,
        subprotocols=["test-token"]
    )
    
    print("WebSocket server is running...")
    await server.wait_closed()

if __name__ == "__main__":
    asyncio.run(main())
